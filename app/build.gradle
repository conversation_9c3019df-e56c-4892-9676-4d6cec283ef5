buildscript {
    dependencies {
//        classpath libs["buf-gradle-plugin"]
    }
    repositories {
        maven { url "https://nexus.bilibili.co/content/groups/public/" }
    }
}

plugins {
    id 'java'
    id 'org.springframework.boot' version '2.7.4'
}


project.ext {
    nettyVersion = '4.1.51.Final'
}

configurations.all {
    resolutionStrategy.cacheChangingModulesFor 30, 'seconds'
    resolutionStrategy {
        force "io.netty:netty-all:${project.ext.nettyVersion}"
        force "io.netty:netty-buffer:${project.ext.nettyVersion}"
        force "io.netty:netty-codec:${project.ext.nettyVersion}"
        force "io.netty:netty-codec-http:${project.ext.nettyVersion}"
        force "io.netty:netty-codec-http2:${project.ext.nettyVersion}"
        force "io.netty:netty-codec-socks:${project.ext.nettyVersion}"
        force "io.netty:netty-common:${project.ext.nettyVersion}"
        force "io.netty:netty-handler:${project.ext.nettyVersion}"
        force "io.netty:netty-handler-proxy:${project.ext.nettyVersion}"
        force "io.netty:netty-resolver:${project.ext.nettyVersion}"
        force "io.netty:netty-resolver-dns:${project.ext.nettyVersion}"
        force "io.netty:netty-transport:${project.ext.nettyVersion}"
    }
}

tasks.withType(Copy) {
    filesMatching("**/*.*") {
        duplicatesStrategy = DuplicatesStrategy.EXCLUDE
    }
}

dependencies {
    /**
     * 基础依赖
     */
    implementation 'com.github.rholder:guava-retrying:2.0.0'
    implementation "co.bilibili.buf:ad_account.crm.acc_grpc_java:${versions['bapis-version']}"
    implementation "co.bilibili.buf:archive.extra_extra.v1_grpc_java:${versions['bapis-version']}"
    implementation "co.bilibili.buf:dynamic_service.feed_grpc_java:${versions['bapis-version']}"
    implementation "co.bilibili.buf:ad_external_grpc_java:${versions['bapis-version']}"
    implementation "co.bilibili.buf:ad_scv.story_new_grpc_java:${versions['bapis-version']}"
    implementation "co.bilibili.buf:ad_audit_grpc_java:${versions['bapis-version']}"
    implementation "co.bilibili.buf:ad_ott_grpc_java:${versions['bapis-version']}"
    implementation "co.bilibili.buf:crm_service.uprating.ratingv3_grpc_java:${versions['bapis-version']}"
    implementation "co.bilibili.buf:archive_service_grpc_java:${versions['bapis-version']}"
    implementation "co.bilibili.buf:community_interface.reply_grpc_java:${versions['bapis-version']}"
    implementation "co.bilibili.buf:community_service.dm.open_grpc_java:${versions['bapis-version']}"
    implementation "co.bilibili.buf:im_biz.interface.v1_grpc_java:${versions['bapis-version']}"
    implementation "co.bilibili.buf:platform_interface.shorturl_grpc_java:${versions['bapis-version']}"
    implementation "co.bilibili.buf:account_service_grpc_java:${versions['bapis-version']}"
    implementation "co.bilibili.buf:passport_service.user_grpc_java:${versions['bapis-version']}"
    implementation "co.bilibili.buf:ad_mgk_grpc_java:${versions['bapis-version']}"
    implementation "co.bilibili.buf:ad_adp.archive_grpc_java:${versions['bapis-version']}"
    implementation "co.bilibili.buf:ad_mgk.material_grpc_java:${versions['bapis-version']}"
    implementation "co.bilibili.buf:ad_mgk.tag_grpc_java:${versions['bapis-version']}"
    implementation "co.bilibili.buf:infra_service.taishan_grpc_java:${versions['bapis-version']}"
    implementation "co.bilibili.buf:ad_archive_grpc_java:${versions['bapis-version']}"
    implementation "co.bilibili.buf:ad_cmc.comment_goods_grpc_java:${versions['bapis-version']}"
    implementation "co.bilibili.buf:ad_cmc.cidgoods_grpc_java:${versions['bapis-version']}"
    implementation "co.bilibili.buf:ad_cmc.dynamic_grpc_java:${versions['bapis-version']}"
    implementation "co.bilibili.buf:ad_adp.component_grpc_java:${versions['bapis-version']}"
    implementation "co.bilibili.buf:ad_adp.log_grpc_java:${versions['bapis-version']}"
    implementation "co.bilibili.buf:ad_component_grpc_java:${versions['bapis-version']}"
    implementation "co.bilibili.buf:ad_scv.anchor_grpc_java:1.29.0.1.mr23100.**************.b2d35ccb3704"
    implementation "co.bilibili.buf:ad_scv.component_group_grpc_java:${versions['bapis-version']}"
    implementation "co.bilibili.buf:ad_scv.story_new_heart_box_grpc_java:${versions['bapis-version']}"
    implementation "co.bilibili.buf:ad_scv.shield_grpc_java:${versions['bapis-version']}"
    implementation "co.bilibili.buf:ad_crm.wallet_grpc_java:${versions['bapis-version']}"
    implementation "co.bilibili.buf:ad_crm.account_grpc_java:${versions['bapis-version']}"
    implementation "co.bilibili.buf:ad_account.label_grpc_java:${versions['bapis-version']}"
    /*implementation ("com.bilibili.adp:launch-biz:${versions['launch-biz']}"){
        exclude group: 'com.squareup.okhttp3', module: 'okhttp'
    }*/

    implementation component["mysql"]
    implementation libs["spring-jdbc"]
    implementation venus['context']
    implementation venus['starter']
    implementation venus['logging']
    implementation venus['naming-discovery']
    implementation venus['infoc']
    implementation component['utility']
    implementation component["http-server"]
    implementation component['http-client']
    implementation component['databus']
    implementation component['databus-v2']
    implementation(component['databus-java'])
    implementation component['rpc-server']
    implementation component['rpc-client']
    implementation component['env']
    testImplementation component['test']
    implementation libs["spring-context-support"]
    implementation libs["caffeine-guava"]
    implementation libs['commons-io']
    implementation libs['commons-codec']
    compileOnly libs['commons-lang']
    implementation libs['jooq']
    implementation libs['jooq-meta']
    implementation libs['lombok']
    implementation libs['bouncycastle']
    implementation libs['swagger']
    implementation libs['jackson-databind']
    implementation libs['redisson']
    implementation libs['springboot-starter-thymeleaf']
    implementation libs['springboot-starter-aop']
    implementation libs['javers']
    implementation libs['jjwt-api']
    implementation libs['jjwt-impl']
    implementation libs['xxl']
    implementation libs['paladin-client']
    implementation libs['paladin-okhttp-client']
    implementation libs['retrofit']
    implementation libs['retrofit-jackson']
    implementation libs['javacv']
    implementation libs['es-rest-client']
    implementation libs['s3']
    implementation libs['mapstruct']
    implementation (libs['mas-api']) {
        exclude group: 'com.bilibili.mas', module: 'mas-common'
        exclude group: 'com.bilibili.adp', module: 'log-api'
        exclude group: 'com.bilibili.adp', module: 'passport-biz'
        exclude group: 'com.bilibili.location', module: 'location-api'
        exclude group: 'org.springframework', module: 'spring-web'
    }
    implementation libs['bvutils']
    implementation libs['easyexcel']
    implementation libs['poi']
    implementation libs['boggart-routing-ds']
    annotationProcessor libs['mapstruct-annotation']
    annotationProcessor libs['lombok-mapstruct-binding']
    annotationProcessor libs['mapstruct-protobuf-spi']
    runtimeOnly libs['jjwt-jackson']
}

bootJar {
    archiveBaseName = "app"
    archiveVersion = ""
}
